# Larasonic 🚀

![<PERSON><PERSON>](public/images/og.webp)

Larasonic is a modern, open-source SaaS starter kit with Laravel, Vue.js, TailwindCSS, and Inertia.

![GitHub Repo stars](https://img.shields.io/github/stars/pushpak1300/Larasonic?style=for-the-badge) [![Licence](https://img.shields.io/github/license/Ileriayo/markdown-badges?style=for-the-badge)](./LICENSE.md) [![Github-sponsors](https://img.shields.io/badge/sponsor-30363D?style=for-the-badge&logo=GitHub-Sponsors&logoColor=#EA4AAA)](https://github.com/sponsors/pushpak1300)

## ✨ Features

- ⚡ 10x Dev Experience
- 🐳 Production Docker Ready
- 🔑 Advanced Authentication
- 💳 Payment Ready
- 🌐 API Ready
- 🎨 Customizable UI
- 🧠 AI Integration Ready
- 📊 FilamentPHP Admin
- ✨ Evolving Features

## Quick Start

```bash
# Clone the repo
<NAME_EMAIL>:pushpak1300/Larasonic.git
cd Larasonic

# Install dependencies
docker run --rm \
    -u "$(id -u):$(id -g)" \
    -v "$(pwd):/var/www/html" \
    -w /var/www/html \
    laravelsail/php83-composer:latest \
    composer install --ignore-platform-reqs

# Setup environment
cp .env.example .env
./vendor/bin/sail up -d
./vendor/bin/sail composer run setup
```

For detailed installation instructions and documentation, visit [docs.larasonic.com](https://docs.larasonic.com).

## Hosting

Proudly hosted and sponsored by [Sevalla.com](https://sevalla.com/?ref=larasonic).

## Security

Report <NAME_EMAIL>

## License

[MIT](https://opensource.org/licenses/MIT)

## Screenshots

| ![Screenshot 4](https://github.com/user-attachments/assets/d7c4eaa9-b547-4952-8ade-4b0ae62aee0e) | ![Screenshot 2](https://github.com/user-attachments/assets/b2d5a28c-9b1b-40bb-82f0-fb9fa932165c) | ![Screenshot 3](https://github.com/user-attachments/assets/d8b15834-bcc2-4028-9d73-a0bb9983c6b7) |
| :----------------------------------------------------------------------------------------------: | :----------------------------------------------------------------------------------------------: | :----------------------------------------------------------------------------------------------: |
| ![Screenshot 1](https://github.com/user-attachments/assets/21c34465-a193-4373-9862-0843f11b957c) | ![Screenshot 5](https://github.com/user-attachments/assets/fba2d341-40c3-4244-8b02-82891c42f2d5) | ![Screenshot 6](https://github.com/user-attachments/assets/37ce7a37-121d-41b1-b3e6-09714cb5c884) |
