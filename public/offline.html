<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>You're Offline - DCCPHub</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #121212;
            color: #e0e0e0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            text-align: center;
        }
        .container {
            max-width: 500px;
            padding: 30px;
            background-color: #1e1e1e;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #ffffff;
        }
        p {
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #f97316;
        }
        .btn {
            background-color: #f97316;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn:hover {
            background-color: #ea580c;
        }
        .small {
            font-size: 0.9rem;
            opacity: 0.7;
            margin-top: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📶</div>
        <h1>You're Offline</h1>
        <p>It seems you're currently offline. Please check your internet connection and try again.</p>
        <p class="small">Some features of DCCPHub are available offline if you've visited them before.</p>
        <button class="btn" onclick="window.location.reload()">Try Again</button>
    </div>
</body>
</html>
