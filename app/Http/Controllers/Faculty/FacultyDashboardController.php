<?php

declare(strict_types=1);

namespace App\Http\Controllers\Faculty;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Faculty;
use App\Models\Classes;
use App\Models\Schedule;
use App\Models\GeneralSettings;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;

final class FacultyDashboardController extends Controller
{
    /**
     * Main faculty dashboard action
     */
    public function __invoke(): Response|RedirectResponse
    {
        /** @var User $user */
        $user = Auth::user();

        // Ensure the user is a faculty member
        if (!$user->isFaculty()) {
            abort(403, 'Only faculty members can access this dashboard.');
        }

        /** @var Faculty $faculty */
        $faculty = $user->faculty;

        if (!$faculty) {
            abort(404, 'Faculty record not found.');
        }

        // Get general settings
        $generalSettings = GeneralSettings::first();
        
        // Get current semester and school year
        $settings = $this->getSettings();
        $currentSemester = $settings['semester'];
        $currentSchoolYear = $settings['schoolYear'];

        // Get faculty's classes and schedules
        $classesData = $this->getClassesData($faculty, $currentSemester, $currentSchoolYear);
        $scheduleData = $this->getScheduleData($faculty, $currentSemester, $currentSchoolYear);
        
        // Calculate stats for the dashboard
        $statsData = $this->calculateStats($faculty, $classesData, $scheduleData);

        // Prepare faculty data
        $facultyData = [
            'name' => $faculty->faculty_full_name,
            'department' => $faculty->department,
            'email' => $faculty->email,
            'office_hours' => $faculty->office_hours,
            'photo_url' => $faculty->photo_url,
        ];

        // Get today's schedule
        $todaysSchedule = $this->getTodaysSchedule($faculty);

        // Get recent activities (placeholder for now)
        $recentActivities = $this->getRecentActivities($faculty);

        return Inertia::render('Faculty/Dashboard', [
            'faculty' => $facultyData,
            'stats' => $statsData,
            'classes' => $classesData,
            'todaysSchedule' => $todaysSchedule,
            'recentActivities' => $recentActivities,
            'user' => $user,
            'semester' => $currentSemester,
            'schoolYear' => $currentSchoolYear,
            'generalSettings' => $generalSettings,
        ]);
    }

    /**
     * Get application settings
     */
    private function getSettings(): array
    {
        $settings = GeneralSettings::query()->first();
        return [
            'semester' => $settings->semester,
            'schoolYear' => $settings->getSchoolYear(),
        ];
    }

    /**
     * Get classes data for the faculty
     */
    private function getClassesData(Faculty $faculty, int $currentSemester, string $currentSchoolYear): array
    {
        $classes = Classes::query()
            ->where('faculty_id', $faculty->id)
            ->where('semester', $currentSemester)
            ->where('school_year', $currentSchoolYear)
            ->with(['subject', 'classEnrollments.student'])
            ->get();

        return $classes->map(function (Classes $class) {
            $enrollments = $class->classEnrollments;
            
            return [
                'id' => $class->id,
                'subject_code' => $class->subject->code ?? 'N/A',
                'subject_title' => $class->subject->title ?? 'N/A',
                'section' => $class->section,
                'schedule' => $class->schedule,
                'room' => $class->room,
                'student_count' => $enrollments->count(),
                'students' => $enrollments->map(function ($enrollment) {
                    $student = $enrollment->student;
                    return [
                        'id' => $student->id,
                        'name' => $student->full_name,
                        'email' => $student->email,
                        'student_id' => $student->student_id,
                    ];
                })->toArray(),
                'color' => $this->generateColorForSubject($class->subject->code ?? 'DEFAULT'),
            ];
        })->toArray();
    }

    /**
     * Get schedule data for the faculty
     */
    private function getScheduleData(Faculty $faculty, int $currentSemester, string $currentSchoolYear): array
    {
        $schedules = Schedule::query()
            ->where('faculty_id', $faculty->id)
            ->where('semester', $currentSemester)
            ->where('school_year', $currentSchoolYear)
            ->with(['subject', 'class'])
            ->get();

        return $schedules->map(function (Schedule $schedule) {
            return [
                'id' => $schedule->id,
                'day' => $schedule->day,
                'start_time' => $schedule->start_time,
                'end_time' => $schedule->end_time,
                'subject_code' => $schedule->subject->code ?? 'N/A',
                'subject_title' => $schedule->subject->title ?? 'N/A',
                'room' => $schedule->room,
                'section' => $schedule->class->section ?? 'N/A',
            ];
        })->toArray();
    }

    /**
     * Calculate stats for the dashboard
     */
    private function calculateStats(Faculty $faculty, array $classesData, array $scheduleData): array
    {
        $totalClasses = count($classesData);
        $totalStudents = array_sum(array_column($classesData, 'student_count'));
        $totalSchedules = count($scheduleData);
        
        // Calculate average class size
        $averageClassSize = $totalClasses > 0 ? round($totalStudents / $totalClasses, 1) : 0;

        return [
            ['label' => 'Total Classes', 'value' => $totalClasses, 'description' => 'Classes you are teaching this semester'],
            ['label' => 'Total Students', 'value' => $totalStudents, 'description' => 'Students enrolled in your classes'],
            ['label' => 'Weekly Schedules', 'value' => $totalSchedules, 'description' => 'Your weekly class schedules'],
            ['label' => 'Avg. Class Size', 'value' => $averageClassSize, 'description' => 'Average students per class'],
        ];
    }

    /**
     * Get today's schedule for the faculty
     */
    private function getTodaysSchedule(Faculty $faculty): array
    {
        $today = now()->format('l'); // Get day name (Monday, Tuesday, etc.)
        
        $todaysSchedules = Schedule::query()
            ->where('faculty_id', $faculty->id)
            ->where('day', $today)
            ->with(['subject', 'class'])
            ->orderBy('start_time')
            ->get();

        return $todaysSchedules->map(function (Schedule $schedule) {
            return [
                'id' => $schedule->id,
                'start_time' => $schedule->start_time,
                'end_time' => $schedule->end_time,
                'subject_code' => $schedule->subject->code ?? 'N/A',
                'subject_title' => $schedule->subject->title ?? 'N/A',
                'room' => $schedule->room,
                'section' => $schedule->class->section ?? 'N/A',
                'color' => $this->generateColorForSubject($schedule->subject->code ?? 'DEFAULT'),
            ];
        })->toArray();
    }

    /**
     * Get recent activities (placeholder)
     */
    private function getRecentActivities(Faculty $faculty): array
    {
        // This is a placeholder - you can implement actual activity tracking later
        return [
            [
                'id' => 1,
                'type' => 'grade_submitted',
                'description' => 'Grades submitted for MATH101',
                'timestamp' => now()->subHours(2)->format('M d, Y H:i'),
            ],
            [
                'id' => 2,
                'type' => 'attendance_recorded',
                'description' => 'Attendance recorded for CS102',
                'timestamp' => now()->subHours(5)->format('M d, Y H:i'),
            ],
        ];
    }

    /**
     * Generate a consistent color for a subject based on its code
     */
    private function generateColorForSubject(string $subjectCode): string
    {
        // Create a hash of the subject code
        $hash = crc32($subjectCode);

        // Define a set of pleasant colors
        $colors = [
            'blue-500', 'green-500', 'purple-500', 'amber-500', 'rose-500',
            'indigo-500', 'emerald-500', 'violet-500', 'orange-500', 'pink-500',
            'cyan-500', 'teal-500', 'fuchsia-500', 'yellow-500', 'red-500',
        ];

        // Use the hash to select a color
        $colorIndex = abs($hash) % count($colors);

        return $colors[$colorIndex];
    }
}
