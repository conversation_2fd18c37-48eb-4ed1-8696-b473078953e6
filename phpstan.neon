includes:
    - vendor/larastan/larastan/extension.neon
    - vendor/nesbot/carbon/extension.neon

parameters:
    paths:
        - app
        - bootstrap
        - database/factories
        - routes
        - config
    level: max
    checkOctaneCompatibility: true
    checkModelProperties: true
    excludePaths:
        - '**/ide-helper.php'
        - '**/ide_helper_models.php'
        - '**/phpstorm.meta.php'
#    ignoreErrors:
#        - '#PHPDoc tag @var#'
#
#    excludePaths:
#        - ./*/*/FileToBeExcluded.php
