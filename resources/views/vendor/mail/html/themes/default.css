/* Base */

body,
body *:not(html):not(style):not(br):not(tr):not(code) {
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  position: relative;
}

body {
  -webkit-text-size-adjust: none;
  background-color: #000000;
  color: #ffffff;
  height: 100%;
  line-height: 1.4;
  margin: 0;
  padding: 0;
  width: 100% !important;
}

p,
ul,
ol,
blockquote {
  line-height: 1.4;
  text-align: left;
}

a {
  color: #ffffff;
}

a img {
  border: none;
}

/* Typography */

h1 {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  margin-top: 0;
  text-align: left;
}

h2 {
  font-size: 16px;
  font-weight: bold;
  margin-top: 0;
  text-align: left;
}

h3 {
  font-size: 14px;
  font-weight: bold;
  margin-top: 0;
  text-align: left;
}

p {
  font-size: 16px;
  line-height: 1.5em;
  margin-top: 0;
  text-align: left;
}

p.sub {
  font-size: 12px;
}

img {
  max-width: 100%;
}

/* Layout */

.wrapper {
  -premailer-cellpadding: 0;
  -premailer-cellspacing: 0;
  -premailer-width: 100%;
  background-color: #000000;
  margin: 0;
  padding: 0;
  width: 100%;
}

.content {
  -premailer-cellpadding: 0;
  -premailer-cellspacing: 0;
  -premailer-width: 100%;
  margin: 0;
  padding: 0;
  width: 100%;
}

/* Header */

.header {
  padding: 25px 0;
  text-align: center;
}

.header a {
  color: #ffffff;
  font-size: 19px;
  font-weight: bold;
  text-decoration: none;
}

/* Logo */

.logo {
  height: 75px;
  max-height: 75px;
  width: 75px;
}

/* Body */

.body {
  -premailer-cellpadding: 0;
  -premailer-cellspacing: 0;
  -premailer-width: 100%;
  background-color: #000000;
  border-bottom: 1px solid #fafafa;
  border-top: 1px solid #fafafa;
  margin: 0;
  padding: 0;
  width: 100%;
}

.inner-body {
  -premailer-cellpadding: 0;
  -premailer-cellspacing: 0;
  -premailer-width: 570px;
  background-color: hsl(240 3.7% 15.9%);
  border-color: #fafafa;
  border-radius: 2px;
  border-width: 1px;
  box-shadow:
    0 2px 0 rgba(255, 255, 255, 0.025),
    2px 4px 0 rgba(255, 255, 255, 0.015);
  margin: 0 auto;
  padding: 0;
  width: 570px;
}

/* Subcopy */

.subcopy {
  border-top: 1px solid #fafafa;
  margin-top: 25px;
  padding-top: 25px;
}

.subcopy p {
  font-size: 14px;
}

/* Footer */

.footer {
  -premailer-cellpadding: 0;
  -premailer-cellspacing: 0;
  -premailer-width: 570px;
  margin: 0 auto;
  padding: 0;
  text-align: center;
  width: 570px;
}

.footer p {
  color: #ffffff;
  font-size: 12px;
  text-align: center;
}

.footer a {
  color: #ffffff;
  text-decoration: underline;
}

/* Tables */

.table table {
  -premailer-cellpadding: 0;
  -premailer-cellspacing: 0;
  -premailer-width: 100%;
  margin: 30px auto;
  width: 100%;
}

.table th {
  border-bottom: 1px solid #000000;
  margin: 0;
  padding-bottom: 8px;
}

.table td {
  color: #ffffff;
  font-size: 15px;
  line-height: 18px;
  margin: 0;
  padding: 10px 0;
}

.content-cell {
  max-width: 100vw;
  padding: 32px;
}

/* Buttons */

.action {
  -premailer-cellpadding: 0;
  -premailer-cellspacing: 0;
  -premailer-width: 100%;
  margin: 30px auto;
  padding: 0;
  text-align: center;
  width: 100%;
  float: unset;
}

.button {
  -webkit-text-size-adjust: none;
  border-radius: 4px;
  color: #fff;
  display: inline-block;
  overflow: hidden;
  text-decoration: none;
}

.button-blue,
.button-primary {
  background-color: #ffffff;
  border-bottom: 8px solid #ffffff;
  border-left: 18px solid #ffffff;
  border-right: 18px solid #ffffff;
  border-top: 8px solid #ffffff;
  color: #000000;
}

.button-green,
.button-success {
  background-color: hsl(142.1 76.2% 36.3%);
  border-bottom: 8px solid hsl(142.1 76.2% 36.3%);
  border-left: 18px solid hsl(142.1 76.2% 36.3%);
  border-right: 18px solid hsl(142.1 76.2% 36.3%);
  border-top: 8px solid hsl(142.1 76.2% 36.3%);
}

.button-red,
.button-error {
  background-color: hsl(0 62.8% 30.6%);
  border-bottom: 8px solid hsl(0 62.8% 30.6%);
  border-left: 18px solid hsl(0 62.8% 30.6%);
  border-right: 18px solid hsl(0 62.8% 30.6%);
  border-top: 8px solid hsl(0 62.8% 30.6%);
}

/* Panels */

.panel {
  border-left: #ffffff solid 4px;
  margin: 21px 0;
}

.panel-content {
  background-color: #000000;
  color: #ffffff;
  padding: 16px;
}

.panel-content p {
  color: #ffffff;
}

.panel-item {
  padding: 0;
}

.panel-item p:last-of-type {
  margin-bottom: 0;
  padding-bottom: 0;
}

/* Utilities */

.break-all {
  word-break: break-all;
}
