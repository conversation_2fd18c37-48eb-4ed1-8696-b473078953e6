<script setup>
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from '@/Components/shadcn/ui/form';
import { Label } from '@/Components/shadcn/ui/label';
import {
  RadioGroup,
  RadioGroupItem,
} from '@/Components/shadcn/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/shadcn/ui/select';
import AutoFormLabel from './AutoFormLabel.vue';
import { beautifyObjectName } from './utils';

defineProps({
  fieldName: { type: String, required: true },
  label: { type: String, required: false },
  required: { type: Boolean, required: false },
  config: { type: Object, required: false },
  disabled: { type: Boolean, required: false },
  options: { type: Array, required: false },
});
</script>

<template>
  <FormField v-slot="slotProps" :name="fieldName">
    <FormItem>
      <AutoFormLabel v-if="!config?.hideLabel" :required="required">
        {{ config?.label || beautifyObjectName(label ?? fieldName) }}
      </AutoFormLabel>
      <FormControl>
        <slot v-bind="slotProps">
          <RadioGroup
            v-if="config?.component === 'radio'"
            :disabled="disabled"
            :orientation="'vertical'"
            v-bind="{ ...slotProps.componentField }"
          >
            <div
              v-for="(option, index) in options"
              :key="option"
              class="mb-2 flex items-center gap-3 space-y-0"
            >
              <RadioGroupItem :id="`${option}-${index}`" :value="option" />
              <Label :for="`${option}-${index}`">{{
                beautifyObjectName(option)
              }}</Label>
            </div>
          </RadioGroup>

          <Select
            v-else
            :disabled="disabled"
            v-bind="{ ...slotProps.componentField }"
          >
            <SelectTrigger class="w-full">
              <SelectValue :placeholder="config?.inputProps?.placeholder" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="option in options"
                :key="option"
                :value="option"
              >
                {{ beautifyObjectName(option) }}
              </SelectItem>
            </SelectContent>
          </Select>
        </slot>
      </FormControl>

      <FormDescription v-if="config?.description">
        {{ config.description }}
      </FormDescription>
      <FormMessage />
    </FormItem>
  </FormField>
</template>
