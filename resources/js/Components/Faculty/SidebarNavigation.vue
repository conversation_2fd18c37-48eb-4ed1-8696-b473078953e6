<template>
  <div class="space-y-1 px-2">
    <template v-for="item in navigation" :key="item.name">
      <Link
        :href="item.href"
        :class="[
          item.current
            ? 'bg-blue-50 border-blue-500 text-blue-700'
            : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900',
          'group flex items-center px-3 py-2 text-sm font-medium border-l-4 transition-colors duration-200'
        ]"
      >
        <component
          :is="item.icon"
          :class="[
            item.current ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500',
            'mr-3 flex-shrink-0 h-5 w-5 transition-colors duration-200'
          ]"
        />
        {{ item.name }}
        
        <!-- Badge for items with notifications -->
        <span
          v-if="item.badge"
          :class="[
            'ml-auto inline-block py-0.5 px-2 text-xs rounded-full',
            item.current ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
          ]"
        >
          {{ item.badge }}
        </span>
      </Link>
    </template>
  </div>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'

defineProps({
  navigation: {
    type: Array,
    required: true
  }
})
</script>
