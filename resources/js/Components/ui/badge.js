import { h } from 'vue'

const badgeVariants = {
  default: 'bg-blue-100 text-blue-800 border-blue-200',
  secondary: 'bg-gray-100 text-gray-800 border-gray-200',
  destructive: 'bg-red-100 text-red-800 border-red-200',
  outline: 'border border-gray-300 text-gray-700 bg-white',
  success: 'bg-green-100 text-green-800 border-green-200',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-200'
}

export const Badge = {
  name: 'Badge',
  props: {
    variant: {
      type: String,
      default: 'default',
      validator: (value) => Object.keys(badgeVariants).includes(value)
    },
    class: {
      type: String,
      default: ''
    }
  },
  setup(props, { slots }) {
    return () => h('span', {
      class: [
        'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors',
        badgeVariants[props.variant],
        props.class
      ].join(' ')
    }, slots.default?.())
  }
}
