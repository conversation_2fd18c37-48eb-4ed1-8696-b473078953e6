<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Mobile sidebar overlay -->
    <div v-if="sidebarOpen" class="fixed inset-0 z-40 lg:hidden">
      <div class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="sidebarOpen = false"></div>
    </div>

    <!-- Mobile sidebar -->
    <div
      :class="[
        'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:hidden',
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      ]"
    >
      <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
        <div class="flex items-center">
          <ApplicationLogo class="h-8 w-auto" />
          <span class="ml-2 text-lg font-semibold text-gray-900">Faculty Portal</span>
        </div>
        <button @click="sidebarOpen = false" class="text-gray-400 hover:text-gray-600">
          <XMarkIcon class="h-6 w-6" />
        </button>
      </div>
      <nav class="mt-4">
        <SidebarNavigation :navigation="navigation" />
      </nav>
    </div>

    <!-- Desktop sidebar -->
    <div class="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
      <div class="flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm">
        <div class="flex items-center h-16 px-4 border-b border-gray-200">
          <ApplicationLogo class="h-8 w-auto" />
          <span class="ml-2 text-lg font-semibold text-gray-900">Faculty Portal</span>
        </div>
        <nav class="mt-4 flex-1">
          <SidebarNavigation :navigation="navigation" />
        </nav>
        
        <!-- User profile section -->
        <div class="flex-shrink-0 border-t border-gray-200 p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <img
                v-if="$page.props.user.profile_photo_url"
                :src="$page.props.user.profile_photo_url"
                :alt="$page.props.user.name"
                class="h-8 w-8 rounded-full"
              />
              <div
                v-else
                class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center"
              >
                <span class="text-sm font-medium text-white">
                  {{ $page.props.user.name.charAt(0) }}
                </span>
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-700">{{ $page.props.user.name }}</p>
              <p class="text-xs text-gray-500">Faculty Member</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content -->
    <div class="lg:pl-64 flex flex-col flex-1">
      <!-- Top navigation -->
      <div class="sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
          <div class="flex items-center">
            <button
              @click="sidebarOpen = true"
              class="text-gray-500 hover:text-gray-600 lg:hidden"
            >
              <Bars3Icon class="h-6 w-6" />
            </button>
            
            <div class="ml-4 lg:ml-0">
              <h1 v-if="$slots.header" class="text-lg font-semibold text-gray-900">
                <slot name="header" />
              </h1>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <button class="text-gray-400 hover:text-gray-500 relative">
              <BellIcon class="h-6 w-6" />
              <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full flex items-center justify-center">
                <span class="text-xs text-white font-medium">3</span>
              </span>
            </button>

            <!-- Quick actions dropdown -->
            <div class="relative">
              <button
                @click="quickActionsOpen = !quickActionsOpen"
                class="text-gray-400 hover:text-gray-500"
              >
                <PlusIcon class="h-6 w-6" />
              </button>
              
              <div
                v-if="quickActionsOpen"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50"
                @click.away="quickActionsOpen = false"
              >
                <a
                  v-for="action in quickActions"
                  :key="action.name"
                  :href="action.href"
                  class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <component :is="action.icon" class="h-4 w-4 mr-3 text-gray-400" />
                  {{ action.name }}
                </a>
              </div>
            </div>

            <!-- Profile dropdown -->
            <Dropdown align="right" width="48">
              <template #trigger>
                <button class="flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none transition duration-150 ease-in-out">
                  <div class="flex items-center">
                    <img
                      v-if="$page.props.user.profile_photo_url"
                      :src="$page.props.user.profile_photo_url"
                      :alt="$page.props.user.name"
                      class="h-8 w-8 rounded-full object-cover"
                    />
                    <div
                      v-else
                      class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center"
                    >
                      <span class="text-sm font-medium text-white">
                        {{ $page.props.user.name.charAt(0) }}
                      </span>
                    </div>
                    <ChevronDownIcon class="ml-2 h-4 w-4" />
                  </div>
                </button>
              </template>

              <template #content>
                <div class="block px-4 py-2 text-xs text-gray-400">
                  Manage Account
                </div>

                <DropdownLink :href="route('profile.show')">
                  Profile
                </DropdownLink>

                <div class="border-t border-gray-100"></div>

                <form @submit.prevent="logout">
                  <DropdownLink as="button">
                    Log Out
                  </DropdownLink>
                </form>
              </template>
            </Dropdown>
          </div>
        </div>
      </div>

      <!-- Page content -->
      <main class="flex-1">
        <div class="py-6">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <slot />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { router } from '@inertiajs/vue3'
import ApplicationLogo from '@/Components/ApplicationLogo.vue'
import Dropdown from '@/Components/Dropdown.vue'
import DropdownLink from '@/Components/DropdownLink.vue'
import SidebarNavigation from '@/Components/Faculty/SidebarNavigation.vue'
import {
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  PlusIcon,
  ChevronDownIcon,
  HomeIcon,
  AcademicCapIcon,
  UsersIcon,
  CalendarIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon,
  DocumentTextIcon,
  CogIcon,
  BookOpenIcon
} from '@heroicons/vue/24/outline'

// Reactive state
const sidebarOpen = ref(false)
const quickActionsOpen = ref(false)

// Navigation items
const navigation = ref([
  { name: 'Dashboard', href: route('faculty.dashboard'), icon: HomeIcon, current: route().current('faculty.dashboard') },
  { name: 'My Classes', href: '#', icon: AcademicCapIcon, current: false },
  { name: 'Students', href: '#', icon: UsersIcon, current: false },
  { name: 'Schedule', href: '#', icon: CalendarIcon, current: false },
  { name: 'Attendance', href: '#', icon: ClipboardDocumentListIcon, current: false },
  { name: 'Grades', href: '#', icon: ChartBarIcon, current: false },
  { name: 'Assignments', href: '#', icon: DocumentTextIcon, current: false },
  { name: 'Resources', href: '#', icon: BookOpenIcon, current: false },
  { name: 'Settings', href: '#', icon: CogIcon, current: false },
])

// Quick actions
const quickActions = ref([
  { name: 'Take Attendance', href: '#', icon: ClipboardDocumentListIcon },
  { name: 'Create Assignment', href: '#', icon: DocumentTextIcon },
  { name: 'Grade Students', href: '#', icon: ChartBarIcon },
  { name: 'Schedule Class', href: '#', icon: CalendarIcon },
])

// Methods
const logout = () => {
  router.post(route('logout'))
}
</script>
