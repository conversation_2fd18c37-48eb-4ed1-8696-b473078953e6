<template>
  <AppLayout title="Faculty Dashboard">
    <template #header>
      <div class="flex items-center justify-between">
        <div>
          <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Faculty Dashboard
          </h2>
          <p class="text-sm text-gray-600 mt-1">
            Welcome back, {{ faculty.name }}
          </p>
        </div>
        <div class="flex items-center space-x-4">
          <div class="text-right">
            <p class="text-sm font-medium text-gray-900">{{ currentSemester }} Semester</p>
            <p class="text-xs text-gray-500">{{ schoolYear }}</p>
          </div>
          <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
            <span class="text-white font-semibold text-lg">
              {{ faculty.name.charAt(0) }}
            </span>
          </div>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div
            v-for="stat in stats"
            :key="stat.label"
            class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"
          >
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <component :is="getStatIcon(stat.label)" class="w-5 h-5 text-white" />
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">
                      {{ stat.label }}
                    </dt>
                    <dd class="text-lg font-medium text-gray-900">
                      {{ stat.value }}
                    </dd>
                  </dl>
                </div>
              </div>
              <div class="mt-2">
                <p class="text-xs text-gray-500">{{ stat.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Today's Schedule -->
          <div class="lg:col-span-1">
            <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
              <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Today's Schedule</h3>
                <p class="text-sm text-gray-500">{{ getCurrentDate() }}</p>
              </div>
              <div class="p-6">
                <div v-if="todaysSchedule.length === 0" class="text-center py-8">
                  <CalendarIcon class="mx-auto h-12 w-12 text-gray-400" />
                  <h3 class="mt-2 text-sm font-medium text-gray-900">No classes today</h3>
                  <p class="mt-1 text-sm text-gray-500">Enjoy your free day!</p>
                </div>
                <div v-else class="space-y-4">
                  <div
                    v-for="schedule in todaysSchedule"
                    :key="schedule.id"
                    class="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50"
                  >
                    <div class="flex-shrink-0">
                      <div :class="`w-3 h-3 rounded-full bg-${schedule.color}`"></div>
                    </div>
                    <div class="ml-3 flex-1">
                      <p class="text-sm font-medium text-gray-900">
                        {{ schedule.subject_code }}
                      </p>
                      <p class="text-xs text-gray-500">
                        {{ schedule.start_time }} - {{ schedule.end_time }}
                      </p>
                      <p class="text-xs text-gray-500">
                        Room {{ schedule.room }} • Section {{ schedule.section }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Classes Overview -->
          <div class="lg:col-span-2">
            <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
              <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-medium text-gray-900">My Classes</h3>
                  <span class="text-sm text-gray-500">{{ classes.length }} classes</span>
                </div>
              </div>
              <div class="p-6">
                <div v-if="classes.length === 0" class="text-center py-8">
                  <BookOpenIcon class="mx-auto h-12 w-12 text-gray-400" />
                  <h3 class="mt-2 text-sm font-medium text-gray-900">No classes assigned</h3>
                  <p class="mt-1 text-sm text-gray-500">Contact administration for class assignments.</p>
                </div>
                <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div
                    v-for="classItem in classes"
                    :key="classItem.id"
                    class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                    @click="viewClassDetails(classItem)"
                  >
                    <div class="flex items-start justify-between">
                      <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-900">
                          {{ classItem.subject_code }}
                        </h4>
                        <p class="text-xs text-gray-500 mt-1">
                          {{ classItem.subject_title }}
                        </p>
                        <p class="text-xs text-gray-500">
                          Section {{ classItem.section }}
                        </p>
                      </div>
                      <div :class="`w-3 h-3 rounded-full bg-${classItem.color} flex-shrink-0`"></div>
                    </div>
                    <div class="mt-3 flex items-center justify-between text-xs text-gray-500">
                      <span>{{ classItem.student_count }} students</span>
                      <span>Room {{ classItem.room }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activities & Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Recent Activities -->
          <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Recent Activities</h3>
            </div>
            <div class="p-6">
              <div class="space-y-4">
                <div
                  v-for="activity in recentActivities"
                  :key="activity.id"
                  class="flex items-start space-x-3"
                >
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckIcon class="w-4 h-4 text-green-600" />
                    </div>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm text-gray-900">{{ activity.description }}</p>
                    <p class="text-xs text-gray-500">{{ activity.timestamp }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-2 gap-4">
                <button
                  v-for="action in quickActions"
                  :key="action.name"
                  @click="action.action"
                  class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <component :is="action.icon" class="w-8 h-8 text-blue-500 mb-2" />
                  <span class="text-sm font-medium text-gray-900">{{ action.name }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import {
  CalendarIcon,
  BookOpenIcon,
  CheckIcon,
  AcademicCapIcon,
  UsersIcon,
  ClockIcon,
  ChartBarIcon,
  PlusIcon,
  DocumentTextIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon
} from '@heroicons/vue/24/outline'

// Props from the controller
const props = defineProps({
  faculty: Object,
  stats: Array,
  classes: Array,
  todaysSchedule: Array,
  recentActivities: Array,
  user: Object,
  semester: Number,
  schoolYear: String,
  generalSettings: Object
})

// Computed properties
const currentSemester = computed(() => {
  const semesterNames = {
    1: '1st',
    2: '2nd',
    3: 'Summer'
  }
  return semesterNames[props.semester] || '1st'
})

// Sample data (will be replaced by real data from props)
const sampleStats = ref([
  { label: 'Total Classes', value: 6, description: 'Classes you are teaching this semester' },
  { label: 'Total Students', value: 180, description: 'Students enrolled in your classes' },
  { label: 'Weekly Schedules', value: 18, description: 'Your weekly class schedules' },
  { label: 'Avg. Class Size', value: 30, description: 'Average students per class' }
])

const sampleTodaysSchedule = ref([
  {
    id: 1,
    subject_code: 'CS101',
    subject_title: 'Introduction to Computer Science',
    start_time: '08:00',
    end_time: '09:30',
    room: '201',
    section: 'A',
    color: 'blue-500'
  },
  {
    id: 2,
    subject_code: 'MATH201',
    subject_title: 'Calculus II',
    start_time: '10:00',
    end_time: '11:30',
    room: '105',
    section: 'B',
    color: 'green-500'
  },
  {
    id: 3,
    subject_code: 'CS102',
    subject_title: 'Data Structures',
    start_time: '14:00',
    end_time: '15:30',
    room: '301',
    section: 'A',
    color: 'purple-500'
  }
])

const sampleClasses = ref([
  {
    id: 1,
    subject_code: 'CS101',
    subject_title: 'Introduction to Computer Science',
    section: 'A',
    room: '201',
    student_count: 35,
    color: 'blue-500'
  },
  {
    id: 2,
    subject_code: 'CS102',
    subject_title: 'Data Structures and Algorithms',
    section: 'A',
    room: '301',
    student_count: 28,
    color: 'purple-500'
  },
  {
    id: 3,
    subject_code: 'MATH201',
    subject_title: 'Calculus II',
    section: 'B',
    room: '105',
    student_count: 42,
    color: 'green-500'
  },
  {
    id: 4,
    subject_code: 'CS201',
    subject_title: 'Database Systems',
    section: 'C',
    room: '205',
    student_count: 25,
    color: 'amber-500'
  }
])

const sampleActivities = ref([
  {
    id: 1,
    type: 'grade_submitted',
    description: 'Grades submitted for CS101 Midterm Exam',
    timestamp: '2 hours ago'
  },
  {
    id: 2,
    type: 'attendance_recorded',
    description: 'Attendance recorded for MATH201 Section B',
    timestamp: '5 hours ago'
  },
  {
    id: 3,
    type: 'assignment_created',
    description: 'New assignment created for CS102',
    timestamp: '1 day ago'
  },
  {
    id: 4,
    type: 'schedule_updated',
    description: 'Schedule updated for next week',
    timestamp: '2 days ago'
  }
])

// Use sample data if props are empty
const stats = computed(() => props.stats?.length ? props.stats : sampleStats.value)
const todaysSchedule = computed(() => props.todaysSchedule?.length ? props.todaysSchedule : sampleTodaysSchedule.value)
const classes = computed(() => props.classes?.length ? props.classes : sampleClasses.value)
const recentActivities = computed(() => props.recentActivities?.length ? props.recentActivities : sampleActivities.value)

// Quick actions
const quickActions = ref([
  {
    name: 'Take Attendance',
    icon: ClipboardDocumentListIcon,
    action: () => console.log('Take Attendance')
  },
  {
    name: 'Grade Students',
    icon: DocumentTextIcon,
    action: () => console.log('Grade Students')
  },
  {
    name: 'View Students',
    icon: UserGroupIcon,
    action: () => console.log('View Students')
  },
  {
    name: 'Create Assignment',
    icon: PlusIcon,
    action: () => console.log('Create Assignment')
  }
])

// Methods
const getStatIcon = (label) => {
  const iconMap = {
    'Total Classes': BookOpenIcon,
    'Total Students': UsersIcon,
    'Weekly Schedules': ClockIcon,
    'Avg. Class Size': ChartBarIcon
  }
  return iconMap[label] || AcademicCapIcon
}

const getCurrentDate = () => {
  return new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const viewClassDetails = (classItem) => {
  console.log('View class details:', classItem)
  // TODO: Navigate to class details page
}
</script>
