<template>
  <FacultyLayout title="Faculty Dashboard">
    <template #header>
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">
            Good {{ getGreeting() }}, {{ faculty.name }}
          </h2>
          <p class="text-sm text-gray-600 mt-1">
            {{ getCurrentDate() }} • {{ currentSemester }} Semester {{ schoolYear }}
          </p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-3">
          <Badge variant="outline" class="text-green-600 border-green-200">
            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            Active
          </Badge>
          <Button size="sm" class="bg-blue-600 hover:bg-blue-700">
            <PlusIcon class="w-4 h-4 mr-2" />
            Quick Action
          </Button>
        </div>
      </div>
    </template>

    <!-- Main Dashboard Content -->
    <div class="space-y-6">
      <!-- Welcome Banner -->
      <Card class="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
        <CardContent class="p-6">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h3 class="text-lg font-semibold">Welcome to your Faculty Dashboard</h3>
              <p class="text-blue-100 mt-1">Manage your classes, students, and academic activities</p>
            </div>
            <div class="mt-4 sm:mt-0">
              <Button variant="secondary" size="sm">
                <BookOpenIcon class="w-4 h-4 mr-2" />
                View Teaching Guide
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Stats Overview -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card v-for="stat in stats" :key="stat.label" class="hover:shadow-md transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">{{ stat.label }}</p>
                <p class="text-2xl font-bold text-gray-900 mt-1">{{ stat.value }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ stat.description }}</p>
              </div>
              <div class="flex-shrink-0">
                <div :class="`w-12 h-12 rounded-lg bg-${stat.color}-100 flex items-center justify-center`">
                  <component :is="getStatIcon(stat.label)" :class="`w-6 h-6 text-${stat.color}-600`" />
                </div>
              </div>
            </div>
            <div class="mt-4">
              <div class="flex items-center text-sm">
                <ArrowTrendingUpIcon class="w-4 h-4 text-green-500 mr-1" />
                <span class="text-green-600 font-medium">+12%</span>
                <span class="text-gray-500 ml-1">from last semester</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Today's Schedule -->
        <div class="lg:col-span-1">
          <Card>
            <CardHeader>
              <div class="flex items-center justify-between">
                <div>
                  <CardTitle class="text-lg">Today's Schedule</CardTitle>
                  <CardDescription>{{ getCurrentDate() }}</CardDescription>
                </div>
                <Button size="sm" variant="outline">
                  <CalendarIcon class="w-4 h-4 mr-2" />
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div v-if="todaysSchedule.length === 0" class="text-center py-8">
                <CalendarIcon class="mx-auto h-12 w-12 text-gray-400" />
                <h3 class="mt-2 text-sm font-medium text-gray-900">No classes today</h3>
                <p class="mt-1 text-sm text-gray-500">Enjoy your free day!</p>
                <Button size="sm" variant="outline" class="mt-4">
                  <PlusIcon class="w-4 h-4 mr-2" />
                  Schedule Office Hours
                </Button>
              </div>
              <div v-else class="space-y-3">
                <div
                  v-for="schedule in todaysSchedule"
                  :key="schedule.id"
                  class="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors cursor-pointer"
                  @click="viewScheduleDetails(schedule)"
                >
                  <div class="flex-shrink-0">
                    <div :class="`w-3 h-3 rounded-full bg-${schedule.color}`"></div>
                  </div>
                  <div class="ml-3 flex-1">
                    <div class="flex items-center justify-between">
                      <p class="text-sm font-medium text-gray-900">
                        {{ schedule.subject_code }}
                      </p>
                      <Badge variant="secondary" class="text-xs">
                        {{ getTimeStatus(schedule) }}
                      </Badge>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">
                      {{ schedule.start_time }} - {{ schedule.end_time }}
                    </p>
                    <p class="text-xs text-gray-500">
                      Room {{ schedule.room }} • Section {{ schedule.section }}
                    </p>
                  </div>
                  <ChevronRightIcon class="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Classes Overview -->
        <div class="lg:col-span-2">
          <Card>
            <CardHeader>
              <div class="flex items-center justify-between">
                <div>
                  <CardTitle class="text-lg">My Classes</CardTitle>
                  <CardDescription>{{ classes.length }} classes this semester</CardDescription>
                </div>
                <div class="flex space-x-2">
                  <Button size="sm" variant="outline">
                    <FunnelIcon class="w-4 h-4 mr-2" />
                    Filter
                  </Button>
                  <Button size="sm">
                    <PlusIcon class="w-4 h-4 mr-2" />
                    Add Class
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div v-if="classes.length === 0" class="text-center py-8">
                <BookOpenIcon class="mx-auto h-12 w-12 text-gray-400" />
                <h3 class="mt-2 text-sm font-medium text-gray-900">No classes assigned</h3>
                <p class="mt-1 text-sm text-gray-500">Contact administration for class assignments.</p>
                <Button size="sm" variant="outline" class="mt-4">
                  Request Class Assignment
                </Button>
              </div>
              <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card
                  v-for="classItem in classes"
                  :key="classItem.id"
                  class="hover:shadow-md transition-all cursor-pointer border-l-4"
                  :class="`border-l-${classItem.color}`"
                  @click="viewClassDetails(classItem)"
                >
                  <CardContent class="p-4">
                    <div class="flex items-start justify-between">
                      <div class="flex-1">
                        <div class="flex items-center space-x-2">
                          <h4 class="text-sm font-semibold text-gray-900">
                            {{ classItem.subject_code }}
                          </h4>
                          <Badge variant="secondary" class="text-xs">
                            Section {{ classItem.section }}
                          </Badge>
                        </div>
                        <p class="text-xs text-gray-600 mt-1 line-clamp-2">
                          {{ classItem.subject_title }}
                        </p>
                        <div class="flex items-center space-x-4 mt-3 text-xs text-gray-500">
                          <div class="flex items-center">
                            <UsersIcon class="w-3 h-3 mr-1" />
                            {{ classItem.student_count }} students
                          </div>
                          <div class="flex items-center">
                            <MapPinIcon class="w-3 h-3 mr-1" />
                            Room {{ classItem.room }}
                          </div>
                        </div>
                      </div>
                      <div class="flex flex-col items-end space-y-2">
                        <div :class="`w-3 h-3 rounded-full bg-${classItem.color}`"></div>
                        <Button size="sm" variant="ghost" class="h-6 w-6 p-0">
                          <EllipsisVerticalIcon class="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <!-- Quick Actions for Class -->
                    <div class="flex space-x-2 mt-4 pt-3 border-t border-gray-100">
                      <Button size="sm" variant="outline" class="flex-1 text-xs">
                        <ClipboardDocumentListIcon class="w-3 h-3 mr-1" />
                        Attendance
                      </Button>
                      <Button size="sm" variant="outline" class="flex-1 text-xs">
                        <ChartBarIcon class="w-3 h-3 mr-1" />
                        Grades
                      </Button>
                      <Button size="sm" variant="outline" class="flex-1 text-xs">
                        <DocumentTextIcon class="w-3 h-3 mr-1" />
                        Assign
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Additional Faculty Features -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Activities -->
        <div class="lg:col-span-2">
          <Card>
            <CardHeader>
              <div class="flex items-center justify-between">
                <CardTitle class="text-lg">Recent Activities</CardTitle>
                <Button size="sm" variant="outline">
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div class="space-y-4">
                <div
                  v-for="activity in recentActivities"
                  :key="activity.id"
                  class="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div class="flex-shrink-0">
                    <div :class="`w-8 h-8 rounded-full flex items-center justify-center ${getActivityColor(activity.type)}`">
                      <component :is="getActivityIcon(activity.type)" class="w-4 h-4" />
                    </div>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">{{ activity.description }}</p>
                    <p class="text-xs text-gray-500 mt-1">{{ activity.timestamp }}</p>
                  </div>
                  <Badge variant="secondary" class="text-xs">
                    {{ activity.type.replace('_', ' ') }}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Quick Actions & Tools -->
        <div>
          <Card>
            <CardHeader>
              <CardTitle class="text-lg">Quick Actions</CardTitle>
              <CardDescription>Frequently used tools</CardDescription>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-1 gap-3">
                <Button
                  v-for="action in quickActions"
                  :key="action.name"
                  @click="action.action"
                  variant="outline"
                  class="justify-start h-auto p-4"
                >
                  <component :is="action.icon" class="w-5 h-5 mr-3 text-blue-500" />
                  <div class="text-left">
                    <div class="font-medium">{{ action.name }}</div>
                    <div class="text-xs text-gray-500">{{ action.description }}</div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>

          <!-- Upcoming Deadlines -->
          <Card class="mt-6">
            <CardHeader>
              <CardTitle class="text-lg">Upcoming Deadlines</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="space-y-3">
                <div
                  v-for="deadline in upcomingDeadlines"
                  :key="deadline.id"
                  class="flex items-center justify-between p-3 rounded-lg border border-gray-200"
                >
                  <div>
                    <p class="text-sm font-medium text-gray-900">{{ deadline.title }}</p>
                    <p class="text-xs text-gray-500">{{ deadline.class }}</p>
                  </div>
                  <Badge :variant="deadline.urgent ? 'destructive' : 'secondary'" class="text-xs">
                    {{ deadline.daysLeft }} days
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Performance Analytics -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle class="text-lg">Performance Analytics</CardTitle>
              <CardDescription>Overview of your teaching metrics</CardDescription>
            </div>
            <div class="flex space-x-2">
              <Button size="sm" variant="outline">
                <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button size="sm" variant="outline">
                <ChartBarIcon class="w-4 h-4 mr-2" />
                Detailed Report
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">94%</div>
              <div class="text-sm text-gray-600">Average Attendance</div>
              <div class="text-xs text-green-600 mt-1">+2% from last month</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">87%</div>
              <div class="text-sm text-gray-600">Assignment Completion</div>
              <div class="text-xs text-green-600 mt-1">+5% from last month</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600">4.8</div>
              <div class="text-sm text-gray-600">Student Rating</div>
              <div class="text-xs text-green-600 mt-1">+0.2 from last semester</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </FacultyLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import FacultyLayout from '@/Layouts/FacultyLayout.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card.js'
import { Button } from '@/Components/ui/button.js'
import { Badge } from '@/Components/ui/badge.js'
import {
  CalendarIcon,
  BookOpenIcon,
  CheckIcon,
  AcademicCapIcon,
  UsersIcon,
  ClockIcon,
  ChartBarIcon,
  PlusIcon,
  DocumentTextIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon,
  ChevronRightIcon,
  EllipsisVerticalIcon,
  MapPinIcon,
  FunnelIcon,
  ArrowTrendingUpIcon,
  ArrowDownTrayIcon,
  ExclamationTriangleIcon,
  BellIcon,
  CogIcon
} from '@heroicons/vue/24/outline'

// Props from the controller
const props = defineProps({
  faculty: Object,
  stats: Array,
  classes: Array,
  todaysSchedule: Array,
  recentActivities: Array,
  user: Object,
  semester: Number,
  schoolYear: String,
  generalSettings: Object
})

// Computed properties
const currentSemester = computed(() => {
  const semesterNames = {
    1: '1st',
    2: '2nd',
    3: 'Summer'
  }
  return semesterNames[props.semester] || '1st'
})

// Enhanced sample data with colors and trends
const sampleStats = ref([
  {
    label: 'Total Classes',
    value: 6,
    description: 'Classes you are teaching this semester',
    color: 'blue',
    trend: '+1 from last semester'
  },
  {
    label: 'Total Students',
    value: 180,
    description: 'Students enrolled in your classes',
    color: 'green',
    trend: '+12% from last semester'
  },
  {
    label: 'Weekly Schedules',
    value: 18,
    description: 'Your weekly class schedules',
    color: 'purple',
    trend: 'Same as last semester'
  },
  {
    label: 'Avg. Class Size',
    value: 30,
    description: 'Average students per class',
    color: 'amber',
    trend: '+2 students from last semester'
  }
])

const sampleTodaysSchedule = ref([
  {
    id: 1,
    subject_code: 'CS101',
    subject_title: 'Introduction to Computer Science',
    start_time: '08:00',
    end_time: '09:30',
    room: '201',
    section: 'A',
    color: 'blue-500'
  },
  {
    id: 2,
    subject_code: 'MATH201',
    subject_title: 'Calculus II',
    start_time: '10:00',
    end_time: '11:30',
    room: '105',
    section: 'B',
    color: 'green-500'
  },
  {
    id: 3,
    subject_code: 'CS102',
    subject_title: 'Data Structures',
    start_time: '14:00',
    end_time: '15:30',
    room: '301',
    section: 'A',
    color: 'purple-500'
  }
])

const sampleClasses = ref([
  {
    id: 1,
    subject_code: 'CS101',
    subject_title: 'Introduction to Computer Science',
    section: 'A',
    room: '201',
    student_count: 35,
    color: 'blue-500'
  },
  {
    id: 2,
    subject_code: 'CS102',
    subject_title: 'Data Structures and Algorithms',
    section: 'A',
    room: '301',
    student_count: 28,
    color: 'purple-500'
  },
  {
    id: 3,
    subject_code: 'MATH201',
    subject_title: 'Calculus II',
    section: 'B',
    room: '105',
    student_count: 42,
    color: 'green-500'
  },
  {
    id: 4,
    subject_code: 'CS201',
    subject_title: 'Database Systems',
    section: 'C',
    room: '205',
    student_count: 25,
    color: 'amber-500'
  }
])

const sampleActivities = ref([
  {
    id: 1,
    type: 'grade_submitted',
    description: 'Grades submitted for CS101 Midterm Exam',
    timestamp: '2 hours ago'
  },
  {
    id: 2,
    type: 'attendance_recorded',
    description: 'Attendance recorded for MATH201 Section B',
    timestamp: '5 hours ago'
  },
  {
    id: 3,
    type: 'assignment_created',
    description: 'New assignment created for CS102 - Data Structures',
    timestamp: '1 day ago'
  },
  {
    id: 4,
    type: 'schedule_updated',
    description: 'Schedule updated for next week',
    timestamp: '2 days ago'
  },
  {
    id: 5,
    type: 'student_message',
    description: 'New message from student about CS301 project',
    timestamp: '3 hours ago'
  }
])

// Upcoming deadlines
const upcomingDeadlines = ref([
  {
    id: 1,
    title: 'Midterm Grades Due',
    class: 'CS101 - Section A',
    daysLeft: 3,
    urgent: true
  },
  {
    id: 2,
    title: 'Assignment Review',
    class: 'MATH201 - Section B',
    daysLeft: 7,
    urgent: false
  },
  {
    id: 3,
    title: 'Final Project Submission',
    class: 'CS301 - Section A',
    daysLeft: 14,
    urgent: false
  }
])

// Use sample data if props are empty
const stats = computed(() => props.stats?.length ? props.stats : sampleStats.value)
const todaysSchedule = computed(() => props.todaysSchedule?.length ? props.todaysSchedule : sampleTodaysSchedule.value)
const classes = computed(() => props.classes?.length ? props.classes : sampleClasses.value)
const recentActivities = computed(() => props.recentActivities?.length ? props.recentActivities : sampleActivities.value)

// Enhanced quick actions with descriptions
const quickActions = ref([
  {
    name: 'Take Attendance',
    description: 'Record student attendance',
    icon: ClipboardDocumentListIcon,
    action: () => console.log('Take Attendance')
  },
  {
    name: 'Grade Assignment',
    description: 'Grade student submissions',
    icon: DocumentTextIcon,
    action: () => console.log('Grade Students')
  },
  {
    name: 'Create Assignment',
    description: 'Create new assignment',
    icon: PlusIcon,
    action: () => console.log('Create Assignment')
  },
  {
    name: 'Schedule Office Hours',
    description: 'Set availability for students',
    icon: CalendarIcon,
    action: () => console.log('Schedule Office Hours')
  },
  {
    name: 'Send Announcement',
    description: 'Notify all students',
    icon: BellIcon,
    action: () => console.log('Send Announcement')
  },
  {
    name: 'Generate Report',
    description: 'Create class performance report',
    icon: ChartBarIcon,
    action: () => console.log('Generate Report')
  }
])

// Methods
const getStatIcon = (label) => {
  const iconMap = {
    'Total Classes': BookOpenIcon,
    'Total Students': UsersIcon,
    'Weekly Schedules': ClockIcon,
    'Avg. Class Size': ChartBarIcon
  }
  return iconMap[label] || AcademicCapIcon
}

const getCurrentDate = () => {
  return new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) return 'morning'
  if (hour < 17) return 'afternoon'
  return 'evening'
}

const getTimeStatus = (schedule) => {
  const now = new Date()
  const currentTime = now.getHours() * 60 + now.getMinutes()
  const startTime = parseInt(schedule.start_time.split(':')[0]) * 60 + parseInt(schedule.start_time.split(':')[1])
  const endTime = parseInt(schedule.end_time.split(':')[0]) * 60 + parseInt(schedule.end_time.split(':')[1])

  if (currentTime < startTime) return 'Upcoming'
  if (currentTime >= startTime && currentTime <= endTime) return 'In Progress'
  return 'Completed'
}

const getActivityIcon = (type) => {
  const iconMap = {
    'grade_submitted': CheckIcon,
    'attendance_recorded': ClipboardDocumentListIcon,
    'assignment_created': DocumentTextIcon,
    'schedule_updated': CalendarIcon,
    'student_message': UserGroupIcon
  }
  return iconMap[type] || CheckIcon
}

const getActivityColor = (type) => {
  const colorMap = {
    'grade_submitted': 'bg-green-100 text-green-600',
    'attendance_recorded': 'bg-blue-100 text-blue-600',
    'assignment_created': 'bg-purple-100 text-purple-600',
    'schedule_updated': 'bg-amber-100 text-amber-600',
    'student_message': 'bg-pink-100 text-pink-600'
  }
  return colorMap[type] || 'bg-gray-100 text-gray-600'
}

const viewClassDetails = (classItem) => {
  console.log('View class details:', classItem)
  // TODO: Navigate to class details page
}

const viewScheduleDetails = (schedule) => {
  console.log('View schedule details:', schedule)
  // TODO: Navigate to schedule details page
}
</script>
