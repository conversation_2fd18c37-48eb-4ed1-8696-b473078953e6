<?php

declare(strict_types=1);

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

final class PWASettings extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('pwa.pwa_app_name', 'TomatoPHP');
        $this->migrator->add('pwa.pwa_short_name', 'Tomato');
        $this->migrator->add('pwa.pwa_start_url', '/');
        $this->migrator->add('pwa.pwa_background_color', '#ffffff');
        $this->migrator->add('pwa.pwa_theme_color', '#000000');
        $this->migrator->add('pwa.pwa_display', 'standalone');
        $this->migrator->add('pwa.pwa_orientation', 'any');
        $this->migrator->add('pwa.pwa_status_bar', '#000000');
        $this->migrator->add('pwa.pwa_icons_72x72', '');
        $this->migrator->add('pwa.pwa_icons_96x96', '');
        $this->migrator->add('pwa.pwa_icons_128x128', '');
        $this->migrator->add('pwa.pwa_icons_144x144', '');
        $this->migrator->add('pwa.pwa_icons_152x152', '');
        $this->migrator->add('pwa.pwa_icons_192x192', '');
        $this->migrator->add('pwa.pwa_icons_384x384', '');
        $this->migrator->add('pwa.pwa_icons_512x512', '');
        $this->migrator->add('pwa.pwa_splash_640x1136', '');
        $this->migrator->add('pwa.pwa_splash_750x1334', '');
        $this->migrator->add('pwa.pwa_splash_828x1792', '');
        $this->migrator->add('pwa.pwa_splash_1125x2436', '');
        $this->migrator->add('pwa.pwa_splash_1242x2208', '');
        $this->migrator->add('pwa.pwa_splash_1242x2688', '');
        $this->migrator->add('pwa.pwa_splash_1536x2048', '');
        $this->migrator->add('pwa.pwa_splash_1668x2224', '');
        $this->migrator->add('pwa.pwa_splash_1668x2388', '');
        $this->migrator->add('pwa.pwa_splash_2048x2732', '');
        $this->migrator->add('pwa.pwa_shortcuts', []);
    }
}
